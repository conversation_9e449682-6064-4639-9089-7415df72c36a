import { EventEmitter } from "events";
import type { TaskQueue } from "./TaskQueue";
import type { QueuedTask, QueueStatus, QueueStats, QueueConfig } from "./types";

/**
 * 队列调度器
 * 控制任务的并发执行，管理正在运行的任务
 */
export class QueueScheduler extends EventEmitter {
  private queue: TaskQueue;
  private config: QueueConfig;
  private running: Map<string, Promise<void>> = new Map();
  private isProcessing: boolean = false;
  private stats: QueueStats;

  constructor(queue: TaskQueue, config: QueueConfig) {
    super();
    this.queue = queue;
    this.config = config;
    
    this.stats = {
      status: "idle",
      totalTasks: 0,
      pendingTasks: 0,
      runningTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
      maxConcurrent: config.maxConcurrent,
      currentConcurrent: 0,
    };

    // 监听队列事件
    this.setupQueueListeners();
  }

  /**
   * 开始处理队列
   */
  async startProcessing(): Promise<void> {
    if (this.isProcessing) {
      return;
    }

    this.isProcessing = true;
    this.updateStats();
    
    // 启动处理循环
    this.processQueue();
  }

  /**
   * 停止处理队列
   */
  async stopProcessing(): Promise<void> {
    this.isProcessing = false;
    
    // 等待所有正在运行的任务完成
    if (this.running.size > 0) {
      await Promise.allSettled(Array.from(this.running.values()));
    }
    
    this.updateStats();
  }

  /**
   * 暂停队列处理
   */
  pause(): void {
    this.queue.pause();
    this.updateStats();
  }

  /**
   * 恢复队列处理
   */
  resume(): void {
    this.queue.resume();
    if (this.isProcessing) {
      this.processQueue();
    }
    this.updateStats();
  }

  /**
   * 清空队列
   */
  clear(): void {
    this.queue.clear();
    this.updateStats();
  }

  /**
   * 设置最大并发数
   */
  setMaxConcurrent(maxConcurrent: number): void {
    this.config.maxConcurrent = maxConcurrent;
    this.stats.maxConcurrent = maxConcurrent;
    
    // 如果正在处理，可能需要启动更多任务
    if (this.isProcessing && !this.queue.isPaused()) {
      this.processQueue();
    }
    
    this.updateStats();
  }

  /**
   * 获取队列统计信息
   */
  getStats(): QueueStats {
    return { ...this.stats };
  }

  /**
   * 获取配置
   */
  getConfig(): QueueConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<QueueConfig>): void {
    this.config = { ...this.config, ...newConfig };
    if (newConfig.maxConcurrent !== undefined) {
      this.stats.maxConcurrent = newConfig.maxConcurrent;
    }
    this.updateStats();
  }

  /**
   * 检查是否可以启动新任务
   */
  canStartNewTask(): boolean {
    return this.isProcessing && 
           !this.queue.isPaused() && 
           this.running.size < this.config.maxConcurrent &&
           !this.queue.isEmpty();
  }

  /**
   * 处理队列中的任务
   */
  private async processQueue(): Promise<void> {
    while (this.canStartNewTask()) {
      const queuedTask = this.queue.dequeue();
      if (!queuedTask) {
        break;
      }

      // 启动任务
      this.startTask(queuedTask);
    }
  }

  /**
   * 启动单个任务
   */
  private startTask(queuedTask: QueuedTask): void {
    const { taskId } = queuedTask;
    
    this.emit("task-started", taskId);
    
    // 创建任务执行Promise
    const taskPromise = this.executeTask(queuedTask)
      .then(() => {
        // 任务成功完成
        this.stats.completedTasks++;
        this.handleTaskCompletion(taskId, true);
      })
      .catch((error) => {
        // 任务失败
        console.error(`任务 ${taskId} 执行失败:`, error);
        this.stats.failedTasks++;
        
        // 尝试重试
        if (this.queue.requeueForRetry(taskId)) {
          console.log(`任务 ${taskId} 已重新排队重试 (${queuedTask.retryCount + 1}/${queuedTask.maxRetries})`);
        }
        
        this.handleTaskCompletion(taskId, false);
      });

    // 记录正在运行的任务
    this.running.set(taskId, taskPromise);
    this.updateStats();
  }

  /**
   * 执行任务（这里需要与TusUploadManager集成）
   */
  private async executeTask(queuedTask: QueuedTask): Promise<void> {
    // 这个方法将在集成到TusUploadManager时实现
    // 现在只是一个占位符
    throw new Error("executeTask method must be implemented by TusUploadManager integration");
  }

  /**
   * 处理任务完成
   */
  private handleTaskCompletion(taskId: string, success: boolean): void {
    // 从运行中的任务列表移除
    this.running.delete(taskId);
    
    // 更新统计信息
    this.updateStats();
    
    // 尝试启动新任务
    if (this.isProcessing && !this.queue.isPaused()) {
      this.processQueue();
    }
  }

  /**
   * 设置队列监听器
   */
  private setupQueueListeners(): void {
    this.queue.on("task-queued", (taskId: string) => {
      this.stats.totalTasks++;
      this.updateStats();
      
      // 如果正在处理且有空闲槽位，尝试启动新任务
      if (this.canStartNewTask()) {
        this.processQueue();
      }
    });

    this.queue.on("queue-status-changed", (status: QueueStatus) => {
      this.stats.status = status;
      this.updateStats();
    });
  }

  /**
   * 更新统计信息并发送事件
   */
  private updateStats(): void {
    this.stats.pendingTasks = this.queue.size();
    this.stats.runningTasks = this.running.size;
    this.stats.currentConcurrent = this.running.size;
    this.stats.status = this.queue.getStatus();
    
    this.emit("queue-stats-updated", this.getStats());
  }
}
