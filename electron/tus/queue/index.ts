/**
 * 队列管理模块
 * 
 * 提供任务队列、调度器和进度聚合功能
 */

export { TaskQueue } from "./TaskQueue";
export { QueueScheduler } from "./QueueScheduler";
export { ProgressAggregator } from "./ProgressAggregator";

export type {
  QueuedTask,
  QueueStatus,
  QueueStats,
  ProgressData,
  AggregatedProgress,
  QueueEvents,
  QueueConfig,
  QueueOperationResult,
} from "./types";

export { TaskPriority } from "./types";

/**
 * 创建默认队列配置
 */
export function createDefaultQueueConfig(): QueueConfig {
  return {
    maxConcurrent: 10, // 用户偏好的并发数
    maxRetries: 3,
    retryDelay: 1000,
    progressUpdateInterval: 500, // 500ms进度更新间隔
    storageUpdateInterval: 2000, // 2秒存储更新间隔
    enablePriority: true,
  };
}
